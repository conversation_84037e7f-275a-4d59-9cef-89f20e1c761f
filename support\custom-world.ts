import { setWorldConstructor, IWorldOptions } from '@cucumber/cucumber';
import path from 'path';
import { <PERSON><PERSON>er, Page, chromium, webkit, firefox,  BrowserContext } from 'playwright';

export class CustomWorld {
  browser!: Browser;
  page!: Page;
  attach: IWorldOptions['attach'];
  errors: string[] = [];

  constructor(options: IWorldOptions) {
    this.attach = options.attach;
    this.loginPage = new LoginPage(this.page);
  }
 
  async launchBrowsers(browserType: string) {
    switch (browserType) {
      case 'chromium':
        this.browser = await chromium.launch({ headless: false });
        break;
      case 'firefox':
        this.browser = await firefox.launch({ headless: false });
        break;
      case 'webkit':
        this.browser = await webkit.launch({ headless: false });
        break;
      default:
        throw new Error(`Unsupported browser type: ${browserType}`);
    }
    const context = await this.browser.newContext();
    recordHar:{ path:path.join(process.cwd(), 'har-files/har.json' )};
    this.page = await context.newPage();
  }

  async closeBrowser() {
    await this.browser?.close();
  }
    addError(error: string) {
    this.errors.push(error);
  }
}
 
  // default launch browser
  // async launchBrowser() {
  //   this.browser = await chromium.launch({headless: false});
  //   const context = await this.browser.newContext();
  //   this.page = await context.newPage();
  // }

setWorldConstructor(CustomWorld);
  

